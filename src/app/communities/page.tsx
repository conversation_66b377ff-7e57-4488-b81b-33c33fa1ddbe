"use client";

import { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import Link from "next/link";
import { useSession } from "next-auth/react";

// Types
interface Community {
  id: string;
  name: string;
  description: string;
  slug: string;
  image: string | null;
  isPrivate: boolean;
  createdAt: string;
  updatedAt: string;
  creatorId: string;
  creator: {
    id: string;
    name: string;
    email: string;
    role: string;
    image: string;
  };
  _count: {
    members: number;
    posts: number;
  };
  members: Array<{
    id: string;
    user: {
      id: string;
      name: string;
      image: string | null;
    };
  }>;
}

interface CommunitiesResponse {
  communities: Community[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Mock communities data (fallback)
const mockCommunities = [
  {
    id: "1",
    name: "Web Development",
    description: "Discuss modern web technologies, frameworks, and best practices",
    slug: "web-development",
    image: null,
    isPrivate: false,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString(),
    creatorId: "admin",
    creator: {
      id: "admin",
      name: "<PERSON> Webdev",
      email: "<EMAIL>",
      role: "ADMIN",
      image: "https://img.daisyui.com/images/profile/demo/<EMAIL>"
    },
    _count: {
      members: 1247,
      posts: 3456
    },
    members: [
      { id: "u1", name: "Alice", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" },
      { id: "u2", name: "Bob", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" },
      { id: "u3", name: "Carol", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" },
      { id: "u4", name: "Dave", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" }
    ]
  },
  {
    id: "2",
    name: "Career Advice",
    description: "Share career tips, job opportunities, and professional growth strategies",
    slug: "career-advice",
    image: null,
    isPrivate: false,
    createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString(),
    creatorId: "career_expert",
    creator: {
      id: "career_expert",
      name: "Eve Career",
      email: "<EMAIL>",
      role: "EDITOR",
      image: "https://img.daisyui.com/images/profile/demo/<EMAIL>"
    },
    _count: {
      members: 892,
      posts: 2134
    },
    members: [
      { id: "u5", name: "Frank", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" },
      { id: "u6", name: "Grace", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" },
      { id: "u7", name: "Heidi", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" }
    ]
  },
  {
    id: "3",
    name: "Design & UX",
    description: "Explore design principles, user experience, and creative inspiration",
    slug: "design-ux",
    image: null,
    isPrivate: false,
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString(),
    creatorId: "design_guru",
    creator: {
      id: "design_guru",
      name: "Ivan Designer",
      email: "<EMAIL>",
      role: "EDITOR",
      image: "https://img.daisyui.com/images/profile/demo/<EMAIL>"
    },
    _count: {
      members: 634,
      posts: 1789
    },
    members: [
      { id: "u8", name: "Judy", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" },
      { id: "u9", name: "Mallory", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" }
    ]
  },
  {
    id: "4",
    name: "Startup Founders",
    description: "Connect with entrepreneurs, share startup experiences and insights",
    slug: "startup-founders",
    image: null,
    isPrivate: true,
    createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString(),
    creatorId: "startup_mentor",
    creator: {
      id: "startup_mentor",
      name: "Oscar Startup",
      email: "<EMAIL>",
      role: "ADMIN",
      image: "https://img.daisyui.com/images/profile/demo/<EMAIL>"
    },
    _count: {
      members: 456,
      posts: 987
    },
    members: [
      { id: "u10", name: "Peggy", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" }
    ]
  },
  {
    id: "5",
    name: "Gaming Hub",
    description: "Discuss games, share reviews, and connect with fellow gamers",
    slug: "gaming-hub",
    image: null,
    isPrivate: false,
    createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString(),
    creatorId: "game_master",
    creator: {
      id: "game_master",
      name: "Trent Gamer",
      email: "<EMAIL>",
      role: "EDITOR",
      image: "https://img.daisyui.com/images/profile/demo/<EMAIL>"
    },
    _count: {
      members: 2134,
      posts: 5678
    },
    members: [
      { id: "u11", name: "Victor", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" },
      { id: "u12", name: "Walter", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" },
      { id: "u13", name: "Xavier", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" },
      { id: "u14", name: "Yvonne", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" }
    ]
  },
  {
    id: "6",
    name: "Photography",
    description: "Share your photos, get feedback, and learn photography techniques",
    slug: "photography",
    image: null,
    isPrivate: false,
    createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString(),
    creatorId: "photo_expert",
    creator: {
      id: "photo_expert",
      name: "Zara Photo",
      email: "<EMAIL>",
      role: "EDITOR",
      image: "https://img.daisyui.com/images/profile/demo/<EMAIL>"
    },
    _count: {
      members: 789,
      posts: 2345
    },
    members: [
      { id: "u15", name: "Amy", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" },
      { id: "u16", name: "Brian", avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>" }
    ]
  }
];

const categories = ["All", "Technology", "Professional", "Creative", "Business", "Entertainment"];

export default function CommunitiesPage() {
  const { data: session } = useSession();
  const [communities, setCommunities] = useState<Community[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("recent");
  const [filterBy, setFilterBy] = useState("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    pages: 0,
  });

  // Fetch communities from API
  const fetchCommunities = useCallback(async (page = 1, search = "") => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
      });

      if (search) {
        params.append('search', search);
      }

      const response = await fetch(`/api/communities?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch communities');
      }

      const data: CommunitiesResponse = await response.json();
      setCommunities(data.communities);
      setPagination(data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch communities');
      setCommunities([]);
    } finally {
      setLoading(false);
    }
  }, [pagination.limit]);

  // Load communities on component mount
  useEffect(() => {
    fetchCommunities();
  }, [fetchCommunities]);

  // Handle search
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      fetchCommunities(1, searchQuery);
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [searchQuery, fetchCommunities]);

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return "Less than an hour ago";
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} days ago`;
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "ADMIN": return "badge-error";
      case "EDITOR": return "badge-warning";
      default: return "badge-info";
    }
  };

  // Filter and sort communities
  const getFilteredAndSortedCommunities = () => {
    let filteredCommunities = communities;

    // Filter by search query (additional client-side filtering)
    if (searchQuery && communities.length > 0) {
      filteredCommunities = filteredCommunities.filter(community =>
        community.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        community.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by type
    if (filterBy === "popular") {
      filteredCommunities = filteredCommunities.filter(community => community._count.members > 10);
    } else if (filterBy === "active") {
      filteredCommunities = filteredCommunities.filter(community => community._count.posts > 5);
    } else if (filterBy === "private") {
      filteredCommunities = filteredCommunities.filter(community => community.isPrivate);
    } else if (filterBy === "public") {
      filteredCommunities = filteredCommunities.filter(community => !community.isPrivate);
    }

    // Sort communities
    switch (sortBy) {
      case "members":
        return filteredCommunities.sort((a, b) => b._count.members - a._count.members);
      case "posts":
        return filteredCommunities.sort((a, b) => b._count.posts - a._count.posts);
      case "name":
        return filteredCommunities.sort((a, b) => a.name.localeCompare(b.name));
      case "recent":
      default:
        return filteredCommunities.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-100 via-base-200 to-base-300">
      <div className="page-container">
        {/* Header */}
        <div className="text-center page-header">
          <h1 className="text-5xl md:text-6xl font-black bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent mb-4">
            Communities
          </h1>
          <p className="text-xl text-base-content/70 max-w-2xl mx-auto">
            Discover and join communities that match your interests. Connect with like-minded people and share knowledge.
          </p>
        </div>

        {/* Controls */}
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10 mb-8">
          <div className="card-body p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-center">
              {/* Search */}
              <div className="flex-1">
                <div className="form-control">
                  <input
                    type="text"
                    placeholder="Search communities..."
                    className="input input-bordered w-full"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              {/* Sort */}
              <div className="form-control">
                <select
                  className="select select-bordered"
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                >
                  <option value="recent">Recent</option>
                  <option value="members">Most Members</option>
                  <option value="posts">Most Posts</option>
                  <option value="name">Name A-Z</option>
                </select>
              </div>

              {/* Filter */}
              <div className="form-control">
                <select
                  className="select select-bordered"
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value)}
                >
                  <option value="all">All Communities</option>
                  <option value="public">Public</option>
                  <option value="private">Private</option>
                  <option value="popular">Popular</option>
                  <option value="active">Active</option>
                </select>
              </div>

              {/* View Mode */}
              <div className="join">
                <button
                  className={`btn btn-sm join-item ${viewMode === "grid" ? "btn-primary" : "btn-outline"}`}
                  onClick={() => setViewMode("grid")}
                  title="Grid View"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 0 01-2-2v-2z" />
                  </svg>
                </button>
                <button
                  className={`btn btn-sm join-item ${viewMode === "list" ? "btn-primary" : "btn-outline"}`}
                  onClick={() => setViewMode("list")}
                  title="List View"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                  </svg>
                </button>
              </div>

              {/* Create Community Button */}
              {session && (
                <Link href="/communities/create" className="btn btn-primary btn-sm rounded-lg">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                  </svg>
                  Create Community
                </Link>
              )}
            </div>
          </div>
        </div>

        {/* Communities Grid/List */}
        {loading ? (
          <div className="card bg-base-100 shadow-xl border border-base-300">
            <div className="card-body text-center py-16">
              <div className="loading loading-spinner loading-lg text-primary"></div>
              <p className="text-base-content/60 mt-4">Loading communities...</p>
            </div>
          </div>
        ) : error ? (
          <div className="card bg-base-100 shadow-xl border border-error/20">
            <div className="card-body text-center py-16">
              <div className="text-6xl mb-4">⚠️</div>
              <h3 className="text-2xl font-bold mb-2 text-error">Error Loading Communities</h3>
              <p className="text-base-content/60 mb-6">{error}</p>
              <button
                onClick={() => fetchCommunities()}
                className="btn btn-primary rounded-lg"
              >
                Try Again
              </button>
            </div>
          </div>
        ) : getFilteredAndSortedCommunities().length === 0 ? (
          <div className="card bg-base-100 shadow-xl border border-base-300">
            <div className="card-body text-center py-16">
              <div className="text-6xl mb-4">🏘️</div>
              <h3 className="text-2xl font-bold mb-2">No Communities Found</h3>
              <p className="text-base-content/60 mb-6">
                {searchQuery ? `No communities match "${searchQuery}"` : "No communities available yet."}
              </p>
              {session && (
                <Link href="/communities/create" className="btn btn-primary rounded-lg">
                  Create the First Community
                </Link>
              )}
            </div>
          </div>
        ) : (
          <div className={viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" : "space-y-4"}>
            {getFilteredAndSortedCommunities().map((community) => (
              <div key={community.id} className={`group card bg-gradient-to-br from-base-100 to-base-200 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-1 border border-primary/5 hover:border-primary/20 ${viewMode === "list" ? "card-side" : ""}`}>
                {/* Community Image */}
                {community.image && (
                  <figure className={`${viewMode === "list" ? "w-48" : "h-48"} overflow-hidden`}>
                    <Image
                      src={community.image}
                      alt={community.name}
                      width={300}
                      height={192}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                  </figure>
                )}

                <div className="card-body">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h2 className="card-title text-xl font-bold group-hover:text-primary transition-colors">
                        <Link href={`/communities/${community.slug}`} className="hover:underline">
                          {community.name}
                        </Link>
                        {community.isPrivate && (
                          <div className="badge badge-warning badge-sm">Private</div>
                        )}
                      </h2>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-base-content/70 text-sm line-clamp-3 mb-4">
                    {community.description}
                  </p>

                  {/* Creator Info */}
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="avatar">
                      <div className="w-8 h-8 rounded-full">
                        <Image
                          src={community.creator.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(community.creator.name)}&background=random`}
                          alt={community.creator.name}
                          width={32}
                          height={32}
                          className="rounded-full"
                        />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-sm">{community.creator.name}</span>
                        <span className={`badge ${getRoleBadgeColor(community.creator.role)} badge-xs`}>
                          {community.creator.role}
                        </span>
                      </div>
                      <div className="text-xs text-base-content/60">
                        Created {formatTimeAgo(community.createdAt)}
                      </div>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-base-content/60 mb-4">
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center space-x-1">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <span>{community._count.members} members</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span>{community._count.posts} posts</span>
                      </span>
                    </div>
                    {/* Avatar Group */}
                    <div className="avatar-group -space-x-6 mt-2">
                      {(Array.isArray(community.members) ? community.members.slice(0, 3) : []).map((member) => (
                        <div className="avatar" key={member.id} title={member.user.name}>
                          <div className="w-12">
                            <Image
                              src={member.user.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(member.user.name)}&background=random`}
                              alt={member.user.name}
                              width={48}
                              height={48}
                              className="rounded-full object-cover"
                              unoptimized
                            />
                          </div>
                        </div>
                      ))}
                      {Array.isArray(community.members) && community._count.members > 3 && (
                        <div className="avatar avatar-placeholder">
                          <div className="bg-neutral text-neutral-content w-12">
                            <span>+{community._count.members - 3}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="card-actions justify-end">
                    <Link
                      href={`/communities/${community.slug}`}
                      className="btn btn-primary btn-sm rounded-lg"
                    >
                      View Community
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="flex justify-center mt-12">
            <div className="join shadow-lg">
              <button
                className="join-item btn btn-outline"
                disabled={pagination.page === 1}
                onClick={() => fetchCommunities(pagination.page - 1, searchQuery)}
              >
                Previous
              </button>
              {Array.from({ length: pagination.pages }, (_, i) => i + 1).map((page) => (
                <button
                  key={page}
                  className={`join-item btn ${page === pagination.page ? 'btn-primary' : 'btn-outline'}`}
                  onClick={() => fetchCommunities(page, searchQuery)}
                >
                  {page}
                </button>
              ))}
              <button
                className="join-item btn btn-outline"
                disabled={pagination.page === pagination.pages}
                onClick={() => fetchCommunities(pagination.page + 1, searchQuery)}
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
